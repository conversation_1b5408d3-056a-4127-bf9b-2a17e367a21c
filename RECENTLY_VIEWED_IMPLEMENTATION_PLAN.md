# Recently Viewed Products - Detailed Implementation Plan

## Overview
This document outlines the implementation plan for adding a "Recently Viewed" feature to track and display recently viewed products for users. The implementation will be designed to work seamlessly with the existing codebase without breaking any current functionality.

## Current Architecture Analysis

### Backend (Django)
- **Framework**: Django with Django REST Framework
- **Database**: PostgreSQL with replica support
- **Authentication**: JWT tokens with NextAuth.js integration
- **Caching**: Redis with fallback to LocMemCache
- **Security**: Comprehensive security monitoring with SecurityEvent model
- **User Model**: Custom `Customer` model extending AbstractUser
- **Product Model**: Comprehensive product model with optimized queries

### Frontend (Next.js)
- **Framework**: Next.js 14 with TypeScript
- **Authentication**: NextAuth.js with JWT
- **State Management**: React Context + Custom hooks
- **Storage**: Custom `useStorage` hook for localStorage/sessionStorage
- **API Integration**: Custom `useApi` hook for API calls

## Implementation Strategy

### Phase 1: Backend Implementation

#### 1.1 Database Model
Create a new model to track recently viewed products:

```python
# users/models.py - Add to existing models
class RecentlyViewedProduct(models.Model):
    user = models.ForeignKey(
        Customer, 
        on_delete=models.CASCADE, 
        related_name="recently_viewed"
    )
    product = models.ForeignKey(
        "products.Product", 
        on_delete=models.CASCADE
    )
    viewed_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ("user", "product")
        ordering = ["-viewed_at"]
        indexes = [
            models.Index(fields=['user', '-viewed_at']),
            models.Index(fields=['product', '-viewed_at']),
        ]
    
    def __str__(self):
        return f"{self.user.email} viewed {self.product.name}"
```

#### 1.2 API Endpoints
Add new endpoints to handle recently viewed products:

```python
# users/urls.py - Add to existing urlpatterns
path("recently-viewed/", RecentlyViewedListView.as_view(), name="recently-viewed-list"),
path("recently-viewed/add/", AddRecentlyViewedView.as_view(), name="recently-viewed-add"),
path("recently-viewed/clear/", ClearRecentlyViewedView.as_view(), name="recently-viewed-clear"),
```

#### 1.3 Views Implementation
Create views following existing patterns:

```python
# users/views.py - Add to existing views
class RecentlyViewedListView(generics.ListAPIView):
    serializer_class = ProductListSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None  # No pagination for recently viewed
    
    def get_queryset(self):
        # Get last 10 recently viewed products
        recently_viewed = RecentlyViewedProduct.objects.filter(
            user=self.request.user
        ).select_related('product')[:10]
        
        product_ids = [rv.product.id for rv in recently_viewed]
        
        # Use existing optimized product queryset
        from products.views import get_optimized_product_queryset
        products = get_optimized_product_queryset(
            Product.objects.filter(id__in=product_ids, is_active=True)
        )
        
        # Maintain order from recently_viewed
        product_dict = {p.id: p for p in products}
        return [product_dict[pid] for pid in product_ids if pid in product_dict]
```

#### 1.4 Automatic Tracking
Integrate with existing ProductDetailView:

```python
# products/views.py - Modify existing ProductDetailView
class ProductDetailView(generics.RetrieveAPIView):
    # ... existing code ...
    
    def retrieve(self, request, *args, **kwargs):
        response = super().retrieve(request, *args, **kwargs)
        
        # Track recently viewed for authenticated users
        if request.user.is_authenticated:
            self._track_recently_viewed(request.user, self.get_object())
        
        return response
    
    def _track_recently_viewed(self, user, product):
        from users.models import RecentlyViewedProduct
        
        # Update or create recently viewed record
        RecentlyViewedProduct.objects.update_or_create(
            user=user,
            product=product,
            defaults={'viewed_at': timezone.now()}
        )
        
        # Keep only last 50 records per user to prevent unlimited growth
        user_recent = RecentlyViewedProduct.objects.filter(user=user)
        if user_recent.count() > 50:
            oldest_records = user_recent[50:]
            RecentlyViewedProduct.objects.filter(
                id__in=[r.id for r in oldest_records]
            ).delete()
```

### Phase 2: Frontend Implementation

#### 2.1 API Integration
Create hooks for recently viewed functionality:

```typescript
// hooks/useRecentlyViewed.ts
export const useRecentlyViewed = () => {
  const { data, loading, read } = useApi(MAIN_URL);
  const { create } = useApi(MAIN_URL);
  const { status } = useSession();
  
  const getRecentlyViewed = useCallback(async () => {
    if (status === 'authenticated') {
      return await read('/api/v1/users/recently-viewed/');
    }
    return [];
  }, [status, read]);
  
  const addToRecentlyViewed = useCallback(async (productId: number) => {
    if (status === 'authenticated') {
      return await create('/api/v1/users/recently-viewed/add/', {
        product_id: productId
      });
    }
  }, [status, create]);
  
  return {
    recentlyViewed: data,
    loading,
    getRecentlyViewed,
    addToRecentlyViewed
  };
};
```

#### 2.2 Component Implementation
Create reusable components:

```typescript
// components/product/RecentlyViewedProducts.tsx
interface RecentlyViewedProductsProps {
  limit?: number;
  showTitle?: boolean;
  className?: string;
}

export const RecentlyViewedProducts: React.FC<RecentlyViewedProductsProps> = ({
  limit = 5,
  showTitle = true,
  className = ""
}) => {
  const { recentlyViewed, loading, getRecentlyViewed } = useRecentlyViewed();
  const { status } = useSession();
  
  useEffect(() => {
    if (status === 'authenticated') {
      getRecentlyViewed();
    }
  }, [status, getRecentlyViewed]);
  
  if (status !== 'authenticated' || !recentlyViewed?.length) {
    return null;
  }
  
  const displayProducts = recentlyViewed.slice(0, limit);
  
  return (
    <div className={`recently-viewed-products ${className}`}>
      {showTitle && (
        <h3 className="text-lg font-semibold mb-4">Recently Viewed</h3>
      )}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {displayProducts.map((product) => (
          <ProductCard key={product.id} {...product} />
        ))}
      </div>
    </div>
  );
};
```

#### 2.3 Integration Points
Add recently viewed to key pages:

1. **Product Detail Page**: Automatic tracking
2. **Home Page**: Display recently viewed section
3. **Account Page**: Full recently viewed list
4. **Shop Page**: Recently viewed sidebar

### Phase 3: Privacy & Performance Considerations

#### 3.1 Privacy Compliance
- Integrate with existing consent management
- Add recently viewed to data export functionality
- Include in data deletion requests
- Respect user privacy preferences

#### 3.2 Performance Optimization
- Use existing Redis caching patterns
- Implement database indexes for efficient queries
- Limit storage to prevent unlimited growth
- Use optimized product querysets

#### 3.3 Fallback for Anonymous Users
Implement localStorage fallback for non-authenticated users:

```typescript
// lib/recentlyViewedManager.ts
class RecentlyViewedManager {
  private storageKey = 'recently_viewed_products';
  private maxItems = 10;
  
  addProduct(product: ProductType) {
    const storage = useStorage('local');
    const existing = this.getProducts();
    
    // Remove if already exists
    const filtered = existing.filter(p => p.id !== product.id);
    
    // Add to beginning
    const updated = [product, ...filtered].slice(0, this.maxItems);
    
    storage.setItem(this.storageKey, JSON.stringify(updated));
  }
  
  getProducts(): ProductType[] {
    const storage = useStorage('local');
    const stored = storage.getItem(this.storageKey);
    return stored ? JSON.parse(stored) : [];
  }
  
  clearProducts() {
    const storage = useStorage('local');
    storage.removeItem(this.storageKey);
  }
}
```

## Implementation Timeline

### Week 1: Backend Foundation
- [ ] Create RecentlyViewedProduct model
- [ ] Run migrations
- [ ] Implement basic API endpoints
- [ ] Add automatic tracking to ProductDetailView

### Week 2: Frontend Integration
- [ ] Create useRecentlyViewed hook
- [ ] Implement RecentlyViewedProducts component
- [ ] Add localStorage fallback for anonymous users
- [ ] Integrate with product detail pages

### Week 3: UI Integration
- [ ] Add recently viewed to home page
- [ ] Create account page section
- [ ] Add mobile-responsive design
- [ ] Implement loading states

### Week 4: Testing & Optimization
- [ ] Write comprehensive tests
- [ ] Performance optimization
- [ ] Privacy compliance verification
- [ ] Documentation and deployment

## Testing Strategy

### Backend Tests
- Model validation and constraints
- API endpoint functionality
- Automatic tracking behavior
- Performance with large datasets

### Frontend Tests
- Component rendering
- Hook functionality
- localStorage fallback
- User interaction flows

## Security Considerations

1. **Data Privacy**: Integrate with existing privacy framework
2. **Authentication**: Respect existing JWT authentication
3. **Rate Limiting**: Use existing throttling patterns
4. **Data Retention**: Automatic cleanup of old records

## Deployment Strategy

1. **Database Migration**: Safe migration with zero downtime
2. **Feature Flags**: Gradual rollout capability
3. **Monitoring**: Integration with existing security monitoring
4. **Rollback Plan**: Easy rollback if issues arise

## Success Metrics

- User engagement with recently viewed products
- Click-through rates from recently viewed sections
- Performance impact on page load times
- User privacy compliance metrics

This implementation plan ensures the recently viewed feature integrates seamlessly with the existing architecture while maintaining all current functionality and following established patterns.

## Detailed Technical Implementation

### Database Schema Details

```sql
-- Migration file: users/migrations/XXXX_add_recently_viewed.py
CREATE TABLE users_recentlyviewedproduct (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users_customer(id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL REFERENCES products_product(id) ON DELETE CASCADE,
    viewed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

CREATE INDEX idx_recently_viewed_user_time ON users_recentlyviewedproduct(user_id, viewed_at DESC);
CREATE INDEX idx_recently_viewed_product_time ON users_recentlyviewedproduct(product_id, viewed_at DESC);
```

### API Endpoint Specifications

#### GET /api/v1/users/recently-viewed/
**Response Format:**
```json
{
  "count": 5,
  "results": [
    {
      "id": 123,
      "name": "Product Name",
      "slug": "product-slug",
      "price": "299.99",
      "images": [{"image": "url"}],
      "category": {"name": "Category", "slug": "category-slug"},
      "brand": {"name": "Brand"},
      "average_rating": 4.5,
      "viewed_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### POST /api/v1/users/recently-viewed/add/
**Request Body:**
```json
{
  "product_id": 123
}
```

**Response:**
```json
{
  "success": true,
  "message": "Product added to recently viewed"
}
```

### Frontend Component Architecture

#### RecentlyViewedProducts Component Props
```typescript
interface RecentlyViewedProductsProps {
  limit?: number;           // Default: 5
  showTitle?: boolean;      // Default: true
  layout?: 'grid' | 'carousel' | 'list'; // Default: 'grid'
  className?: string;
  onProductClick?: (product: ProductType) => void;
  showClearButton?: boolean; // Default: false
}
```

#### Integration with Existing Components
```typescript
// app/page.tsx - Home page integration
export default function HomePage() {
  return (
    <MainHOF>
      {/* Existing home content */}
      <FeaturedProducts />
      <CategoryShowcase />

      {/* New recently viewed section */}
      <RecentlyViewedProducts
        limit={6}
        layout="carousel"
        className="my-8"
      />

      {/* Rest of home content */}
    </MainHOF>
  );
}
```

### Performance Optimization Strategies

#### 1. Database Query Optimization
```python
# Optimized query with prefetch_related
def get_recently_viewed_products(user, limit=10):
    recently_viewed = RecentlyViewedProduct.objects.filter(
        user=user
    ).select_related('product__category', 'product__brand').prefetch_related(
        'product__images'
    ).order_by('-viewed_at')[:limit]

    return [rv.product for rv in recently_viewed if rv.product.is_active]
```

#### 2. Redis Caching Strategy
```python
# Cache recently viewed products for 1 hour
def get_cached_recently_viewed(user_id, limit=10):
    cache_key = f"recently_viewed:{user_id}:{limit}"
    cached_data = cache.get(cache_key)

    if cached_data is None:
        products = get_recently_viewed_products(user_id, limit)
        cache.set(cache_key, products, timeout=3600)  # 1 hour
        return products

    return cached_data
```

#### 3. Frontend Caching
```typescript
// Use React Query for client-side caching
const useRecentlyViewedQuery = () => {
  const { status } = useSession();

  return useQuery({
    queryKey: ['recently-viewed'],
    queryFn: fetchRecentlyViewed,
    enabled: status === 'authenticated',
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};
```

### Privacy and GDPR Compliance

#### 1. Consent Integration
```python
# Check user consent before tracking
def track_product_view(user, product):
    if not user.has_analytics_consent():
        return False

    RecentlyViewedProduct.objects.update_or_create(
        user=user,
        product=product,
        defaults={'viewed_at': timezone.now()}
    )
    return True
```

#### 2. Data Export Integration
```python
# Add to existing data export functionality
def export_user_data(user):
    data = {
        # ... existing export data
        'recently_viewed': [
            {
                'product_name': rv.product.name,
                'product_slug': rv.product.slug,
                'viewed_at': rv.viewed_at.isoformat(),
            }
            for rv in user.recently_viewed.all()
        ]
    }
    return data
```

#### 3. Data Deletion Integration
```python
# Add to existing data deletion process
def delete_user_data(user):
    # ... existing deletion logic

    # Delete recently viewed products
    RecentlyViewedProduct.objects.filter(user=user).delete()
```

### Mobile Responsiveness

#### Responsive Grid Layout
```css
/* styles/recently-viewed.css */
.recently-viewed-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: 768px) {
  .recently-viewed-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .recently-viewed-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}
```

#### Mobile Carousel Implementation
```typescript
// Use existing Swiper integration
const RecentlyViewedCarousel = ({ products }: { products: ProductType[] }) => {
  return (
    <Swiper
      spaceBetween={16}
      slidesPerView={2}
      breakpoints={{
        640: { slidesPerView: 3 },
        768: { slidesPerView: 4 },
        1024: { slidesPerView: 5 },
      }}
    >
      {products.map((product) => (
        <SwiperSlide key={product.id}>
          <ProductCard {...product} />
        </SwiperSlide>
      ))}
    </Swiper>
  );
};
```

### Error Handling and Fallbacks

#### Backend Error Handling
```python
def track_recently_viewed_safe(user, product):
    try:
        track_product_view(user, product)
    except Exception as e:
        # Log error but don't break the main flow
        logger.error(f"Failed to track recently viewed: {e}")
        # Optionally send to error monitoring service
```

#### Frontend Error Boundaries
```typescript
const RecentlyViewedWithErrorBoundary = () => {
  return (
    <ErrorBoundary
      fallback={<div>Unable to load recently viewed products</div>}
      onError={(error) => console.error('Recently viewed error:', error)}
    >
      <RecentlyViewedProducts />
    </ErrorBoundary>
  );
};
```

### Analytics and Monitoring

#### Track User Engagement
```python
# Add analytics events
def track_recently_viewed_click(user, product, source='recently_viewed'):
    SecurityEvent.objects.create(
        user=user,
        event_type='PRODUCT_INTERACTION',
        ip_address=get_client_ip(request),
        details={
            'action': 'recently_viewed_click',
            'product_id': product.id,
            'product_name': product.name,
            'source': source,
        }
    )
```

#### Performance Monitoring
```python
# Monitor query performance
def monitor_recently_viewed_performance():
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT
                COUNT(*) as total_records,
                AVG(EXTRACT(EPOCH FROM (NOW() - viewed_at))) as avg_age_seconds
            FROM users_recentlyviewedproduct
            WHERE viewed_at > NOW() - INTERVAL '30 days'
        """)
        return cursor.fetchone()
```

This comprehensive implementation plan provides all the technical details needed to implement the recently viewed feature while maintaining the existing codebase integrity and following established patterns.
